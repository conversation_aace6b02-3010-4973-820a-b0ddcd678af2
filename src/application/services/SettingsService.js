import { ISettingsService } from './ISettingsService.js';

/**
 * 设置服务实现
 * 基于存储仓储的应用设置管理
 */
export class SettingsService extends ISettingsService {
  /**
   * 构造函数
   * @param {IStorageRepository} storageRepository - 存储仓储
   * @param {ICacheService} cacheService - 缓存服务
   * @param {Object} defaultSettings - 默认设置
   */
  constructor(storageRepository, cacheService, defaultSettings = {}) {
    super();
    this.storage = storageRepository;
    this.cache = cacheService;
    this.defaultSettings = defaultSettings;
    this.namespace = 'settings';
    this.cacheNamespace = 'settings';
    this.cacheTtl = 5 * 60 * 1000; // 5分钟缓存
    this.changeListeners = new Set();
    
    // 设置模式定义
    this.schema = {
      theme: { type: 'string', enum: ['light', 'dark', 'auto'], default: 'auto' },
      language: { type: 'string', enum: ['zh-CN', 'en-US'], default: 'zh-CN' },
      autoSave: { type: 'boolean', default: true },
      saveInterval: { type: 'number', min: 1000, max: 60000, default: 5000 },
      maxMemories: { type: 'number', min: 100, max: 10000, default: 1000 },
      enableNotifications: { type: 'boolean', default: true },
      enableSync: { type: 'boolean', default: false },
      debugMode: { type: 'boolean', default: false }
    };

    // 监听存储变化
    this._setupStorageListener();
  }

  /**
   * 创建设置键
   * @param {string} key - 原始键
   * @returns {string} 完整的设置键
   */
  _createKey(key) {
    return this.storage.createNamespacedKey ? 
      this.storage.createNamespacedKey(this.namespace, key) : 
      `${this.namespace}:${key}`;
  }

  /**
   * 创建缓存键
   * @param {string} key - 原始键
   * @returns {string} 缓存键
   */
  _createCacheKey(key) {
    return `${this.cacheNamespace}:${key}`;
  }

  /**
   * 设置存储变化监听器
   */
  _setupStorageListener() {
    if (this.storage.onChanged) {
      this.storage.onChanged((changes, areaName) => {
        for (const [key, change] of Object.entries(changes)) {
          if (key.startsWith(`${this.namespace}:`)) {
            const settingKey = key.replace(`${this.namespace}:`, '');
            
            // 清除缓存
            this.cache.delete(this._createCacheKey(settingKey), this.cacheNamespace);
            
            // 通知监听器
            this._notifyListeners(settingKey, change.newValue, change.oldValue);
          }
        }
      });
    }
  }

  /**
   * 通知变化监听器
   * @param {string} key - 设置键
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   */
  _notifyListeners(key, newValue, oldValue) {
    for (const listener of this.changeListeners) {
      try {
        listener(key, newValue, oldValue);
      } catch (error) {
        console.error('Settings change listener error:', error);
      }
    }
  }

  /**
   * 获取设置值
   * @param {string} key - 设置键
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 设置值
   */
  async get(key, defaultValue = null) {
    try {
      // 先从缓存获取
      const cacheKey = this._createCacheKey(key);
      let value = await this.cache.get(cacheKey, this.cacheNamespace);
      
      if (value !== null) {
        return value;
      }

      // 从存储获取
      const storageKey = this._createKey(key);
      const result = await this.storage.get(storageKey);
      value = result[storageKey];

      // 如果没有值，使用默认值
      if (value === undefined || value === null) {
        value = defaultValue !== null ? defaultValue : 
                this.defaultSettings[key] !== undefined ? this.defaultSettings[key] :
                this.schema[key]?.default !== undefined ? this.schema[key].default :
                null;
      }

      // 缓存结果
      if (value !== null) {
        await this.cache.set(cacheKey, value, { 
          ttl: this.cacheTtl, 
          namespace: this.cacheNamespace 
        });
      }

      return value;
    } catch (error) {
      console.error(`Failed to get setting '${key}':`, error);
      return defaultValue;
    }
  }

  /**
   * 设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<void>}
   */
  async set(key, value) {
    // 验证值
    const isValid = await this.validate(key, value);
    if (!isValid) {
      throw new Error(`Invalid value for setting '${key}': ${value}`);
    }

    try {
      const storageKey = this._createKey(key);
      const oldValue = await this.get(key);

      // 保存到存储
      await this.storage.set({ [storageKey]: value });

      // 更新缓存
      const cacheKey = this._createCacheKey(key);
      await this.cache.set(cacheKey, value, { 
        ttl: this.cacheTtl, 
        namespace: this.cacheNamespace 
      });

      // 通知监听器
      this._notifyListeners(key, value, oldValue);
    } catch (error) {
      throw new Error(`Failed to set setting '${key}': ${error.message}`);
    }
  }

  /**
   * 删除设置
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async remove(key) {
    try {
      const storageKey = this._createKey(key);
      const oldValue = await this.get(key);

      // 从存储删除
      await this.storage.remove(storageKey);

      // 从缓存删除
      const cacheKey = this._createCacheKey(key);
      await this.cache.delete(cacheKey, this.cacheNamespace);

      // 通知监听器
      this._notifyListeners(key, null, oldValue);

      return true;
    } catch (error) {
      console.error(`Failed to remove setting '${key}':`, error);
      return false;
    }
  }

  /**
   * 获取所有设置
   * @returns {Promise<Object>} 所有设置
   */
  async getAll() {
    try {
      const result = await this.storage.getByNamespace ? 
        await this.storage.getByNamespace(this.namespace) :
        await this._getAllByPrefix();

      // 合并默认设置
      const allSettings = { ...this.defaultSettings };
      
      // 添加模式默认值
      for (const [key, schema] of Object.entries(this.schema)) {
        if (schema.default !== undefined && allSettings[key] === undefined) {
          allSettings[key] = schema.default;
        }
      }

      // 覆盖存储的设置
      Object.assign(allSettings, result);

      return allSettings;
    } catch (error) {
      console.error('Failed to get all settings:', error);
      return { ...this.defaultSettings };
    }
  }

  /**
   * 通过前缀获取所有设置（兼容方法）
   * @returns {Promise<Object>} 设置对象
   */
  async _getAllByPrefix() {
    const allKeys = await this.storage.keys();
    const settingKeys = allKeys.filter(key => key.startsWith(`${this.namespace}:`));
    
    if (settingKeys.length === 0) {
      return {};
    }

    const result = await this.storage.get(settingKeys);
    const settings = {};

    for (const [key, value] of Object.entries(result)) {
      const settingKey = key.replace(`${this.namespace}:`, '');
      settings[settingKey] = value;
    }

    return settings;
  }

  /**
   * 批量设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setMultiple(settings) {
    const validatedSettings = {};
    const storageData = {};

    // 验证所有设置
    for (const [key, value] of Object.entries(settings)) {
      const isValid = await this.validate(key, value);
      if (!isValid) {
        throw new Error(`Invalid value for setting '${key}': ${value}`);
      }
      validatedSettings[key] = value;
      storageData[this._createKey(key)] = value;
    }

    try {
      // 保存到存储
      await this.storage.set(storageData);

      // 更新缓存
      for (const [key, value] of Object.entries(validatedSettings)) {
        const cacheKey = this._createCacheKey(key);
        await this.cache.set(cacheKey, value, { 
          ttl: this.cacheTtl, 
          namespace: this.cacheNamespace 
        });
      }

      // 通知监听器
      for (const [key, value] of Object.entries(validatedSettings)) {
        const oldValue = await this.get(key);
        this._notifyListeners(key, value, oldValue);
      }
    } catch (error) {
      throw new Error(`Failed to set multiple settings: ${error.message}`);
    }
  }

  /**
   * 重置设置为默认值
   * @param {string[]} keys - 要重置的键，不传则重置所有
   * @returns {Promise<void>}
   */
  async reset(keys) {
    try {
      if (keys && Array.isArray(keys)) {
        // 重置指定键
        const resetData = {};
        for (const key of keys) {
          const defaultValue = this.defaultSettings[key] !== undefined ? 
            this.defaultSettings[key] : 
            this.schema[key]?.default;
          
          if (defaultValue !== undefined) {
            resetData[key] = defaultValue;
          }
        }
        await this.setMultiple(resetData);
      } else {
        // 重置所有设置
        await this.storage.removeByNamespace ? 
          await this.storage.removeByNamespace(this.namespace) :
          await this._removeAllByPrefix();
        
        // 清除缓存
        await this.cache.clear(this.cacheNamespace);
      }
    } catch (error) {
      throw new Error(`Failed to reset settings: ${error.message}`);
    }
  }

  /**
   * 通过前缀删除所有设置（兼容方法）
   * @returns {Promise<void>}
   */
  async _removeAllByPrefix() {
    const allKeys = await this.storage.keys();
    const settingKeys = allKeys.filter(key => key.startsWith(`${this.namespace}:`));
    
    if (settingKeys.length > 0) {
      await this.storage.remove(settingKeys);
    }
  }

  /**
   * 检查设置是否存在
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    try {
      const storageKey = this._createKey(key);
      return await this.storage.has(storageKey);
    } catch (error) {
      console.error(`Failed to check setting existence '${key}':`, error);
      return false;
    }
  }

  /**
   * 监听设置变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    this.changeListeners.add(callback);

    return () => {
      this.changeListeners.delete(callback);
    };
  }

  /**
   * 获取设置模式
   * @returns {Promise<Object>} 设置模式定义
   */
  async getSchema() {
    return { ...this.schema };
  }

  /**
   * 验证设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<boolean>} 是否有效
   */
  async validate(key, value) {
    const schema = this.schema[key];
    if (!schema) {
      return true; // 未定义模式的键允许任何值
    }

    try {
      // 类型检查
      if (schema.type && typeof value !== schema.type) {
        return false;
      }

      // 枚举检查
      if (schema.enum && !schema.enum.includes(value)) {
        return false;
      }

      // 数值范围检查
      if (schema.type === 'number') {
        if (schema.min !== undefined && value < schema.min) {
          return false;
        }
        if (schema.max !== undefined && value > schema.max) {
          return false;
        }
      }

      // 字符串长度检查
      if (schema.type === 'string') {
        if (schema.minLength !== undefined && value.length < schema.minLength) {
          return false;
        }
        if (schema.maxLength !== undefined && value.length > schema.maxLength) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error(`Validation error for setting '${key}':`, error);
      return false;
    }
  }

  /**
   * 导出设置
   * @returns {Promise<Object>} 设置数据
   */
  async export() {
    try {
      const allSettings = await this.getAll();
      return {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        settings: allSettings
      };
    } catch (error) {
      throw new Error(`Failed to export settings: ${error.message}`);
    }
  }

  /**
   * 导入设置
   * @param {Object} data - 设置数据
   * @param {boolean} merge - 是否合并现有设置
   * @returns {Promise<void>}
   */
  async import(data, merge = true) {
    if (!data || !data.settings) {
      throw new Error('Invalid settings data format');
    }

    try {
      if (!merge) {
        await this.reset();
      }

      await this.setMultiple(data.settings);
    } catch (error) {
      throw new Error(`Failed to import settings: ${error.message}`);
    }
  }
}
