/**
 * Memory实体 单元测试
 */

import { Memory } from '../Memory.js';

describe('Memory实体', () => {
  const deviceId = 'test-device-123';

  describe('创建记忆', () => {
    test('应该能够创建新的记忆实例', () => {
      const data = {
        title: '测试记忆',
        content: '这是测试内容',
        category: 'test-category',
        url: 'https://example.com',
        tags: ['测试', '单元测试']
      };

      const memory = Memory.create(data, deviceId);

      expect(memory.id).toBeDefined();
      expect(memory.title).toBe(data.title);
      expect(memory.content).toBe(data.content);
      expect(memory.category).toBe(data.category);
      expect(memory.url).toBe(data.url);
      expect(memory.tags).toEqual(data.tags);
      expect(memory.version).toBe(1);
      expect(memory.lastModifiedBy).toBe(deviceId);
      expect(memory.isDeleted).toBe(false);
      expect(memory.createdAt).toBeDefined();
      expect(memory.lastModified).toBeDefined();
    });

    test('应该为缺失的字段提供默认值', () => {
      const data = {
        title: '最小记忆',
        content: '内容'
      };

      const memory = Memory.create(data, deviceId);

      expect(memory.title).toBe(data.title);
      expect(memory.content).toBe(data.content);
      expect(memory.category).toBe('');
      expect(memory.url).toBe('');
      expect(memory.tags).toEqual([]);
    });

    test('应该为空标题提供默认值', () => {
      const data = {
        content: '只有内容的记忆'
      };

      const memory = Memory.create(data, deviceId);

      expect(memory.title).toBe('无标题记忆');
      expect(memory.content).toBe(data.content);
    });
  });

  describe('更新记忆', () => {
    test('应该能够更新记忆实例', () => {
      const originalMemory = Memory.create({
        title: '原始标题',
        content: '原始内容',
        category: 'original-category'
      }, deviceId);

      const updates = {
        title: '更新后的标题',
        content: '更新后的内容'
      };

      const updatedMemory = Memory.update(originalMemory, updates, deviceId);

      expect(updatedMemory.id).toBe(originalMemory.id);
      expect(updatedMemory.title).toBe(updates.title);
      expect(updatedMemory.content).toBe(updates.content);
      expect(updatedMemory.category).toBe(originalMemory.category); // 未更新的字段保持不变
      expect(updatedMemory.version).toBe(originalMemory.version + 1);
      expect(updatedMemory.lastModifiedBy).toBe(deviceId);
      expect(updatedMemory.lastModified).not.toBe(originalMemory.lastModified);
    });

    test('应该只更新提供的字段', () => {
      const originalMemory = Memory.create({
        title: '原始标题',
        content: '原始内容',
        category: 'original-category',
        tags: ['原始标签']
      }, deviceId);

      const updates = {
        title: '新标题'
      };

      const updatedMemory = Memory.update(originalMemory, updates, deviceId);

      expect(updatedMemory.title).toBe(updates.title);
      expect(updatedMemory.content).toBe(originalMemory.content);
      expect(updatedMemory.category).toBe(originalMemory.category);
      expect(updatedMemory.tags).toEqual(originalMemory.tags);
    });
  });

  describe('软删除记忆', () => {
    test('应该能够软删除记忆', () => {
      const memory = Memory.create({
        title: '要删除的记忆',
        content: '内容'
      }, deviceId);

      const deletedMemory = Memory.softDelete(memory, deviceId);

      expect(deletedMemory.id).toBe(memory.id);
      expect(deletedMemory.isDeleted).toBe(true);
      expect(deletedMemory.deletedAt).toBeDefined();
      expect(deletedMemory.deletedBy).toBe(deviceId);
      expect(deletedMemory.version).toBe(memory.version + 1);
      expect(deletedMemory.lastModifiedBy).toBe(deviceId);
    });
  });

  describe('数据验证', () => {
    test('应该验证有效的记忆数据', () => {
      const validData = {
        title: '有效的标题',
        content: '有效的内容',
        category: 'valid-category',
        url: 'https://example.com',
        tags: ['标签1', '标签2']
      };

      const result = Memory.validate(validData);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    test('应该检测空标题', () => {
      const invalidData = {
        title: '',
        content: '内容'
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('标题不能为空');
    });

    test('应该检测空内容', () => {
      const invalidData = {
        title: '标题',
        content: ''
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('内容不能为空');
    });

    test('应该检测标题长度超限', () => {
      const invalidData = {
        title: 'a'.repeat(201), // 超过200字符
        content: '内容'
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('标题长度不能超过200个字符');
    });

    test('应该检测内容长度超限', () => {
      const invalidData = {
        title: '标题',
        content: 'a'.repeat(50001) // 超过50000字符
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('内容长度不能超过50000个字符');
    });

    test('应该检测无效的标签格式', () => {
      const invalidData = {
        title: '标题',
        content: '内容',
        tags: 'not-an-array'
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('标签必须是数组');
    });

    test('应该检测标签数量超限', () => {
      const invalidData = {
        title: '标题',
        content: '内容',
        tags: Array(21).fill('标签') // 超过20个标签
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('标签数量不能超过20个');
    });

    test('应该检测无效的URL格式', () => {
      const invalidData = {
        title: '标题',
        content: '内容',
        url: 123 // 不是字符串
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('URL必须是字符串');
    });

    test('应该返回多个验证错误', () => {
      const invalidData = {
        title: '',
        content: '',
        tags: 'not-an-array'
      };

      const result = Memory.validate(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
      expect(result.errors).toContain('标题不能为空');
      expect(result.errors).toContain('内容不能为空');
      expect(result.errors).toContain('标签必须是数组');
    });
  });

  describe('搜索匹配', () => {
    const memory = Memory.create({
      title: 'JavaScript学习笔记',
      content: '学习JavaScript的基础知识，包括变量、函数、对象等',
      category: 'study',
      tags: ['JavaScript', '编程', '学习'],
      createdAt: '2023-01-15T10:00:00.000Z'
    }, deviceId);

    test('应该匹配关键词搜索', () => {
      const query = { keyword: 'JavaScript' };
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = { keyword: '变量' };
      expect(Memory.matchesQuery(memory, query2)).toBe(true);

      const query3 = { keyword: 'Python' };
      expect(Memory.matchesQuery(memory, query3)).toBe(false);
    });

    test('应该匹配分类搜索', () => {
      const query = { category: 'study' };
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = { category: 'work' };
      expect(Memory.matchesQuery(memory, query2)).toBe(false);
    });

    test('应该匹配标签搜索', () => {
      const query = { tags: ['JavaScript'] };
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = { tags: ['编程', '学习'] };
      expect(Memory.matchesQuery(memory, query2)).toBe(true);

      const query3 = { tags: ['Python'] };
      expect(Memory.matchesQuery(memory, query3)).toBe(false);
    });

    test('应该匹配日期范围搜索', () => {
      const query = {
        dateFrom: '2023-01-01T00:00:00.000Z',
        dateTo: '2023-12-31T23:59:59.999Z'
      };
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = {
        dateFrom: '2023-02-01T00:00:00.000Z'
      };
      expect(Memory.matchesQuery(memory, query2)).toBe(false);
    });

    test('应该匹配组合搜索条件', () => {
      const query = {
        keyword: 'JavaScript',
        category: 'study',
        tags: ['编程']
      };
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = {
        keyword: 'JavaScript',
        category: 'work' // 分类不匹配
      };
      expect(Memory.matchesQuery(memory, query2)).toBe(false);
    });

    test('应该跳过已删除的记忆', () => {
      const deletedMemory = Memory.softDelete(memory, deviceId);
      const query = { keyword: 'JavaScript' };
      
      expect(Memory.matchesQuery(deletedMemory, query)).toBe(false);
    });

    test('关键词搜索应该不区分大小写', () => {
      const query = { keyword: 'javascript' }; // 小写
      expect(Memory.matchesQuery(memory, query)).toBe(true);

      const query2 = { keyword: 'JAVASCRIPT' }; // 大写
      expect(Memory.matchesQuery(memory, query2)).toBe(true);
    });
  });

  describe('排序功能', () => {
    const memories = [
      Memory.create({
        title: 'B记忆',
        content: '内容B',
        createdAt: '2023-01-15T10:00:00.000Z',
        lastModified: '2023-01-16T10:00:00.000Z'
      }, deviceId),
      Memory.create({
        title: 'A记忆',
        content: '内容A',
        createdAt: '2023-01-10T10:00:00.000Z',
        lastModified: '2023-01-20T10:00:00.000Z'
      }, deviceId),
      Memory.create({
        title: 'C记忆',
        content: '内容C',
        createdAt: '2023-01-20T10:00:00.000Z',
        lastModified: '2023-01-12T10:00:00.000Z'
      }, deviceId)
    ];

    test('应该能够按标题排序', () => {
      const sortedAsc = Memory.sort([...memories], 'title', 'asc');
      expect(sortedAsc[0].title).toBe('A记忆');
      expect(sortedAsc[1].title).toBe('B记忆');
      expect(sortedAsc[2].title).toBe('C记忆');

      const sortedDesc = Memory.sort([...memories], 'title', 'desc');
      expect(sortedDesc[0].title).toBe('C记忆');
      expect(sortedDesc[1].title).toBe('B记忆');
      expect(sortedDesc[2].title).toBe('A记忆');
    });

    test('应该能够按创建时间排序', () => {
      const sortedAsc = Memory.sort([...memories], 'createdAt', 'asc');
      expect(sortedAsc[0].title).toBe('A记忆'); // 最早创建
      expect(sortedAsc[2].title).toBe('C记忆'); // 最晚创建

      const sortedDesc = Memory.sort([...memories], 'createdAt', 'desc');
      expect(sortedDesc[0].title).toBe('C记忆'); // 最晚创建
      expect(sortedDesc[2].title).toBe('A记忆'); // 最早创建
    });

    test('应该能够按最后修改时间排序', () => {
      const sortedAsc = Memory.sort([...memories], 'lastModified', 'asc');
      expect(sortedAsc[0].title).toBe('C记忆'); // 最早修改
      expect(sortedAsc[2].title).toBe('A记忆'); // 最晚修改

      const sortedDesc = Memory.sort([...memories], 'lastModified', 'desc');
      expect(sortedDesc[0].title).toBe('A记忆'); // 最晚修改
      expect(sortedDesc[2].title).toBe('C记忆'); // 最早修改
    });

    test('应该使用默认排序（按最后修改时间降序）', () => {
      const sorted = Memory.sort([...memories]);
      expect(sorted[0].title).toBe('A记忆'); // 最晚修改
      expect(sorted[2].title).toBe('C记忆'); // 最早修改
    });
  });
});
